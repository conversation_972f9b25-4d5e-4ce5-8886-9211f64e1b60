'use client';

import { useState, useEffect } from 'react';
import { useAuthContext } from './use-auth-context';

interface MFAStatus {
  isRequired: boolean;
  isVerified: boolean;
  isConfigured: boolean;
  loading: boolean;
  error: string | null;
}

interface UseMFAGuardResult {
  mfaStatus: MFAStatus;
  showMFAChallenge: boolean;
  handleMFASuccess: () => void;
  checkMFAStatus: () => Promise<void>;
  resetMFAState: () => void;
}

export function useMFAGuard(): UseMFAGuardResult {
  const { user } = useAuthContext();
  const [mfaStatus, setMfaStatus] = useState<MFAStatus>({
    isRequired: false,
    isVerified: false,
    isConfigured: false,
    loading: true,
    error: null,
  });
  const [showMFAChallenge, setShowMFAChallenge] = useState(false);

  const checkMFAStatus = async () => {
    if (!user) {
      setMfaStatus(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      setMfaStatus(prev => ({ ...prev, loading: true, error: null }));

      // Check admin verification status (includes MFA status)
      const response = await fetch('/api/admin/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to verify admin status');
      }

      const result = await response.json();

      if (!result.isAdmin) {
        throw new Error('Admin access required');
      }

      const newMfaStatus = {
        isRequired: result.mfaRequired || false,
        isVerified: result.mfaVerified || false,
        isConfigured: result.mfaConfigured || false,
        loading: false,
        error: null
      };

      setMfaStatus(newMfaStatus);

      // Show MFA challenge if MFA is required but not verified
      if (newMfaStatus.isRequired && !newMfaStatus.isVerified) {
        if (newMfaStatus.isConfigured) {
          setShowMFAChallenge(true);
        } else {
          // MFA is required but not configured - redirect to setup
          window.location.href = '/admin/security/mfa';
        }
      } else {
        setShowMFAChallenge(false);
      }

    } catch (error: any) {
      setMfaStatus({
        isRequired: false,
        isVerified: false,
        isConfigured: false,
        loading: false,
        error: error.message || 'Failed to check MFA status',
      });
    }
  };

  const handleMFASuccess = () => {
    setMfaStatus(prev => ({
      ...prev,
      isVerified: true,
    }));
    setShowMFAChallenge(false);
  };

  const resetMFAState = () => {
    setMfaStatus({
      isRequired: false,
      isVerified: false,
      isConfigured: false,
      loading: true,
      error: null,
    });
    setShowMFAChallenge(false);
  };

  // Check MFA status when user changes
  useEffect(() => {
    if (user) {
      checkMFAStatus();
    } else {
      resetMFAState();
    }
  }, [user]);

  // Periodic check for MFA session expiration (every 5 minutes)
  useEffect(() => {
    if (!user || !mfaStatus.isRequired) return;

    const interval = setInterval(() => {
      checkMFAStatus();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [user, mfaStatus.isRequired]);

  return {
    mfaStatus,
    showMFAChallenge,
    handleMFASuccess,
    checkMFAStatus,
    resetMFAState,
  };
}
