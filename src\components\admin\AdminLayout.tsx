'use client';

// Admin Layout Component
// Date: 16/01/2025 - Updated: 16/06/2025
// Task: adminSystemImpl002 - Sprint 1 Milestone 1.2
// SECURITY UPDATE: Fixed CRITICAL client-side authentication bypass vulnerability
// MFA UPDATE: Integrated Multi-Factor Authentication support
// CVSS Score: 9.8 (Critical) - Now uses server-side verification with MFA

import React from 'react';
import { useAuthContext } from '@/hooks/use-auth-context';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { AdminNavigation } from './AdminNavigation';
import { AdminBreadcrumb } from './AdminBreadcrumb';
import MFAChallenge from './MFAChallenge';
import { useMFAGuard } from '@/hooks/use-mfa-guard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ShieldAlert } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
  }>;
}

export function AdminLayout({
  children,
  title,
  description,
  breadcrumbs = []
}: AdminLayoutProps) {
  const { user, loading } = useAuthContext();
  const [isValidAdmin, setIsValidAdmin] = useState(false);
  const [verifyingAdmin, setVerifyingAdmin] = useState(true);
  const { mfaStatus, showMFAChallenge, handleMFASuccess } = useMFAGuard();
  const router = useRouter();

  useEffect(() => {
    async function verifyAdminAccess() {
      // SECURITY FIX: Server-side verification instead of client-side checks
      if (!user) {
        router.push('/');
        return;
      }

      try {
        // Call server-side verification API (cannot be bypassed)
        const response = await fetch('/api/admin/verify', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        });

        if (response.ok) {
          const { isAdmin } = await response.json();
          setIsValidAdmin(isAdmin);
        } else {
          console.warn('🔒 SECURITY: Admin verification failed', {
            status: response.status,
            userId: user.id
          });
          router.push('/');
        }
      } catch (error) {
        console.error('🚨 SECURITY: Admin verification error:', error);
        router.push('/');
      } finally {
        setVerifyingAdmin(false);
      }
    }

    if (!loading) {
      verifyAdminAccess();
    }
  }, [user, loading, router]);

  const handleMFACancel = () => {
    router.push('/');
  };

  // Loading state - show while verifying authentication
  if (loading || verifyingAdmin || mfaStatus.loading) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-10rem)]">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
        <div className="ml-4 text-muted-foreground">
          {loading ? 'Loading...' : verifyingAdmin ? 'Verifying admin access...' : 'Checking MFA status...'}
        </div>
      </div>
    );
  }

  // Show MFA Challenge if required
  if (showMFAChallenge && isValidAdmin) {
    return (
      <MFAChallenge
        onSuccess={handleMFASuccess}
        onCancel={handleMFACancel}
        title="MFA Verification Required"
        description="As a super administrator, you must verify your identity with two-factor authentication to access the administrative panel."
      />
    );
  }

  // SECURITY: Only render admin content if server-side verification passed
  if (!isValidAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] text-center p-4">
        <Card className="w-full max-w-md shadow-lg glow-purple">
          <CardHeader className="items-center">
            <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
            <CardTitle className="text-3xl font-bold text-destructive">Access Denied</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">You don't have admin privileges.</p>
            <p className="text-muted-foreground text-sm mt-2">Contact an administrator if you believe this is an error.</p>
            <p className="text-xs text-muted-foreground mt-2 font-mono">
              🔒 Server-side verification required
            </p>
            <Button
              onClick={() => router.push('/')}
              className="w-full mt-4"
            >
              Go to Homepage
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Admin layout
  return (
    <div className="min-h-screen bg-transparent">
      {/* Fixed Admin Header - positioned below the main navbar */}
      <div className="fixed top-14 left-0 right-0 z-40 border-b border-violet-900/20 bg-gradient-to-r from-slate-900/95 to-slate-800/95 backdrop-blur-md shadow-md">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white drop-shadow-lg font-mono tracking-tight">
                <span className="text-violet-400">&lt;</span>
                <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Admin Panel</span>
                <span className="text-violet-400">/&gt;</span>
              </h1>
              {user && (
                <div className="text-sm text-muted-foreground font-mono">
                  Welcome, {user.displayName || user.email}
                </div>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-xs font-mono text-violet-400/80">
                Admin Mode
              </div>
              <div className="w-2 h-2 bg-violet-500 rounded-full animate-pulse shadow-sm shadow-violet-400/30"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Content with top padding to account for fixed header */}
      <div className="pt-36 container mx-auto px-4 pb-6">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Sidebar Navigation - Sticky positioned with improved background */}
          <div className="lg:col-span-3">
            <div className="sticky top-44">
              <AdminNavigation />
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-9">
            {/* Breadcrumb */}
            {breadcrumbs.length > 0 && (
              <div className="mb-6">
                <AdminBreadcrumb items={breadcrumbs} />
              </div>
            )}

            {/* Page Header */}
            {(title || description) && (
              <div className="mb-6">
                {title && (
                  <h2 className="text-3xl font-bold mb-2 font-mono tracking-tight">
                    <span className="text-violet-400">&lt;</span>
                    <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">{title}</span>
                    <span className="text-violet-400">/&gt;</span>
                  </h2>
                )}
                {description && (
                  <p className="text-muted-foreground font-mono text-sm">{description}</p>
                )}
              </div>
            )}

            {/* Page Content */}
            <div className="space-y-6">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AdminLayout;
